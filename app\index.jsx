import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect, useRef } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();
  const hasRedirected = useRef(false);

  useEffect(() => {
    if (!authChecked || hasRedirected.current) {
      return;
    }

    console.log('🏠 Root index page accessed');
    console.log('🏠 Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');
    console.log('🏠 User state:', user ? (user.isGuest ? 'guest' : 'user') : 'none');

    // 检查是否是刷新操作（URL包含dashboard相关路径）
    const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
    const isDashboardRefresh = currentUrl.includes('dashboard') || currentUrl.includes('tab=');

    if (isDashboardRefresh && user) {
      console.log('🔄 检测到dashboard页面刷新，跳过根页面重定向');
      hasRedirected.current = true;
      return;
    }

    // 只在真正访问根路径时进行重定向
    if (user) {
      console.log('👤 Redirecting logged in user to dashboard');
      hasRedirected.current = true;
      router.replace('/(dashboard)');
    } else {
      console.log('🚪 Redirecting to welcome');
      hasRedirected.current = true;
      router.replace('/welcome');
    }
  }, [user, authChecked]);

  // 如果还在检查认证状态，直接返回空视图，避免闪屏
  if (!authChecked) {
    return null;
  }

  // 这个页面不应该被显示，因为会自动重定向，返回空视图
  return null;
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})